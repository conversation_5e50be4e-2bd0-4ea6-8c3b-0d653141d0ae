<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BusinessInfoController extends Controller
{
    /**
     * Get business information from webhook token
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getBusinessInfo(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'job_id' => 'required|string'
            ]);

            $token = $request->input('token');
            $jobId = $request->input('job_id');

            // Find campaign by token and job_id
            $campaign = JobNotificationCampaign::where('admin_token', $token)
                ->where('job_id', $jobId)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid token or job not found'
                ], 404);
            }

            // Check if token is expired
            if ($campaign->token_expires_at->isPast()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Token has expired'
                ], 403);
            }

            // Get business info from recipient (assuming single business per token for now)
            $recipient = $campaign->recipients()->first();
            
            if (!$recipient) {
                return response()->json([
                    'success' => false,
                    'error' => 'No business found for this token'
                ], 404);
            }

            // Check if business already has an account
            $existingUser = \App\Models\User::where('email', $recipient->business_email)->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'business' => [
                        'id' => $recipient->business_id,
                        'name' => $recipient->business_name,
                        'email' => $recipient->business_email,
                        'phone' => $recipient->business_phone,
                        'address' => $recipient->business_address,
                        'distance' => $recipient->distance,
                    ],
                    'job' => [
                        'id' => $campaign->job_id,
                        'title' => $campaign->job_title,
                        'description' => $campaign->job_description,
                        'zip_code' => $campaign->job_zip_code,
                        'category' => $campaign->job_category,
                        'budget' => $campaign->job_budget,
                        'service_date' => $campaign->service_date,
                    ],
                    'has_existing_account' => $existingUser ? true : false,
                    'user_id' => $existingUser ? $existingUser->id : null,
                    'certificates_verified' => $existingUser ? ($existingUser->certificates_status === 'approved') : false,
                    'token_expires_at' => $campaign->token_expires_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get business info from token: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve business information'
            ], 500);
        }
    }

    /**
     * Get job details from token
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getJobDetails(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'job_id' => 'required|string'
            ]);

            $token = $request->input('token');
            $jobId = $request->input('job_id');

            // Find campaign by token and job_id
            $campaign = JobNotificationCampaign::where('admin_token', $token)
                ->where('job_id', $jobId)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid token or job not found'
                ], 404);
            }

            // Check if token is expired
            if ($campaign->token_expires_at->isPast()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Token has expired'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'job' => [
                        'id' => $campaign->job_id,
                        'title' => $campaign->job_title,
                        'description' => $campaign->job_description,
                        'zip_code' => $campaign->job_zip_code,
                        'address' => $campaign->job_address,
                        'latitude' => $campaign->job_latitude,
                        'longitude' => $campaign->job_longitude,
                        'category' => $campaign->job_category,
                        'budget' => $campaign->job_budget,
                        'budget_min' => $campaign->budget_min,
                        'budget_max' => $campaign->budget_max,
                        'currency' => $campaign->currency,
                        'service_date' => $campaign->service_date,
                        'location' => $campaign->location,
                        'service' => $campaign->service,
                        'pricing_parameters' => $campaign->pricing_parameters,
                        'note' => $campaign->note,
                        'extra' => $campaign->extra,
                        'price' => $campaign->price,
                        'duration' => $campaign->duration,
                        'frequency' => $campaign->frequency,
                        'created_at' => $campaign->job_created_at,
                    ],
                    'customer' => [
                        'name' => $campaign->customer_name,
                        'email' => $campaign->customer_email,
                        'phone' => $campaign->customer_phone,
                    ],
                    'token_expires_at' => $campaign->token_expires_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get job details from token: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve job details'
            ], 500);
        }
    }
}
