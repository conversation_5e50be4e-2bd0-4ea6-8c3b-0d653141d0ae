<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Job Notification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the simplified job notification 
    | system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Search Radius
    |--------------------------------------------------------------------------
    |
    | The default radius (in miles) to use when searching for businesses
    | around a job's location.
    |
    */
    'default_radius' => (int) env('JOB_NOTIFICATION_DEFAULT_RADIUS', 25),

    /*
    |--------------------------------------------------------------------------
    | Maximum Search Radius
    |--------------------------------------------------------------------------
    |
    | The maximum radius (in miles) that can be used when searching for 
    | businesses.
    |
    */
    'max_radius' => (int) env('JOB_NOTIFICATION_MAX_RADIUS', 75),

    /*
    |--------------------------------------------------------------------------
    | Admin Email
    |--------------------------------------------------------------------------
    |
    | The email address of the admin who will receive job notification
    | campaign approval requests.
    |
    */
    'admin_email' => env('JOB_NOTIFICATION_ADMIN_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------
    | Admin Contact Information
    |--------------------------------------------------------------------------
    |
    | Admin contact information that will be included in business notification
    | emails for customer support and inquiries.
    |
    */
    'admin_contact_email' => env('JOB_NOTIFICATION_ADMIN_CONTACT_EMAIL', '<EMAIL>'),
    'admin_contact_phone' => env('JOB_NOTIFICATION_ADMIN_CONTACT_PHONE', '******-123-4567'),

    /*
    |--------------------------------------------------------------------------
    | Token Expiry
    |--------------------------------------------------------------------------
    |
    | The number of hours that a notification approval token remains valid.
    |
    */
    'token_expiry_hours' => (int) env('JOB_NOTIFICATION_TOKEN_EXPIRY_HOURS', 24),

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the retry mechanism when no businesses are found
    | initially for job notifications.
    |
    */

    /*
    | Maximum number of retry attempts before rejecting a campaign
    */
    'max_retry_attempts' => (int) env('JOB_NOTIFICATION_MAX_RETRY_ATTEMPTS', 3),

    /*
    | Delay in seconds before retrying after scraping is triggered
    | Default: 600 seconds (10 minutes)
    */
    'retry_after_scraping_delay' => (int) env('JOB_NOTIFICATION_RETRY_AFTER_SCRAPING_DELAY', 600),

    /*
    | Base delay in seconds for exponential backoff between retries
    | Default: 120 seconds (2 minutes)
    */
    'retry_base_delay' => (int) env('JOB_NOTIFICATION_RETRY_BASE_DELAY', 120),

    /*
    | Maximum delay in seconds for exponential backoff
    | Default: 1800 seconds (30 minutes)
    */
    'retry_max_delay' => (int) env('JOB_NOTIFICATION_RETRY_MAX_DELAY', 1800),
];