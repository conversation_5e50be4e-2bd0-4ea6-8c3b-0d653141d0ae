# Product Requirements Document: Job Posted Webhook API

## Overview

This document outlines the complete flow and logic for the `POST /api/webhooks/job-posted` endpoint, which receives job posting events from external systems and processes them to send notifications to businesses within a 25-mile radius of the job location.

## API Endpoint Details

### Webhook Endpoint

- **URL**: `POST /api/webhooks/job-posted`
- **Controller**: `App\Http\Controllers\API\WebhookController@handleJobPostedEvent`
- **Authentication**: Token-based (X-Webhook-Token header or webhook_token parameter)
- **Content-Type**: `application/json`

### Health Check Endpoint

- **URL**: `GET /api/webhooks/health`
- **Purpose**: Monitor webhook service status

### Frontend Integration Endpoints

#### Business Info API

- **URL**: `GET /api/business-info?token={token}&job_id={jobId}`
- **Purpose**: Retrieve business information from webhook token
- **Response**: Business details, job info, account status, certificate verification status

#### Job Details API

- **URL**: `GET /api/business-info/job-details?token={token}&job_id={jobId}`
- **Purpose**: Retrieve complete job details from webhook token
- **Response**: Full job information, customer details, token expiry

### Customer Job History Endpoints

#### Customer Job History List

- **URL**: `GET /api/customer/job-history`
- **Authentication**: Required (auth:api middleware)
- **Purpose**: Get all jobs created by customer through webhook notifications
- **Query Parameters**:
  - `page` (optional): Page number for pagination
  - `per_page` (optional): Items per page (default: 15)
  - `status` (optional): Filter by campaign status
- **Response**: List of job campaigns with claim status, pagination info

#### Customer Job Campaign Details

- **URL**: `GET /api/customer/job-history/{campaignId}`
- **Authentication**: Required (auth:api middleware)
- **Purpose**: Get detailed information about specific job campaign
- **Response**: Campaign details, claim information, provider details, bids, bookings

## Request Specification

### Authentication

- **Method**: Token-based authentication
- **Header**: `X-Webhook-Token: {token}`
- **Alternative**: `webhook_token` parameter in request body
- **Security**: Uses `hash_equals()` for timing-attack safe comparison

### Request Payload Schema

```json
{
  "event_type": "job_posted",
  "event_id": "string (required)",
  "timestamp": "date (required)",
  "job_data": {
    "job_id": "string (required)",
    "title": "string (required)",
    "description": "string (optional)",
    "category_id": "integer (optional)",
    "zip_code": "string (required)",
    "address": "string (optional)",
    "latitude": "numeric (optional)",
    "longitude": "numeric (optional)",
    "budget_min": "numeric (optional)",
    "budget_max": "numeric (optional)",
    "currency": "string (optional, 3 chars)",
    "customer_email": "email (required)",
    "customer_name": "string (required)",
    "phone": "string (optional)",
    "email": "email (optional)",
    "service_date": "string (optional)",
    "location": "string (optional)",
    "service": "string (optional)",
    "pricing_parameters": "string (optional)",
    "note": "string (optional)",
    "extra": "string (optional)",
    "price": "numeric (optional)",
    "duration": "string (optional)",
    "frequency": "string (optional)",
    "created_at": "date (required)"
  }
}
```

### Validation Rules

- **event_type**: Must be exactly "job_posted"
- **event_id**: Required string for tracking
- **timestamp**: Valid date format (ISO 8601, Y-m-d H:i:s, etc.)
- **job_data.zip_code**: Required for business discovery
- **job_data.customer_email**: Valid email format
- **job_data.created_at**: Valid date format

## Complete Flow Architecture

### 1. Webhook Reception & Validation

```
External System → WebhookController → Validation → Authentication → Payload Processing
```

**Steps:**

1. **Authentication Verification**

   - Extract token from header or body
   - Compare with configured webhook token
   - Reject if authentication fails

2. **Payload Validation**

   - Validate all required fields
   - Check data types and formats
   - Normalize timestamps to Y-m-d H:i:s format
   - Return 422 error for validation failures

3. **Event Logging**

   - Log incoming webhook with event details
   - Include source IP, user agent, and job information
   - Track for audit and debugging purposes

### 2. Data Transformation & Job Dispatch

```
Validated Data → Transformation → ProcessJobNotificationJob → Queue Processing
```

**Steps:**

1. **Data Transformation**

   - Map `job_data` to `data` field for job processing
   - Map `job_id` to `id` for compatibility
   - Preserve original event structure

2. **Job Dispatch**

   - Dispatch `ProcessJobNotificationJob` to queue
   - Pass transformed event data
   - Return immediate success response

### 3. Background Job Processing

```
ProcessJobNotificationJob → Campaign Creation → Business Discovery → Notification Setup
```

**Steps:**

1. **Campaign Creation**

   - Create `JobNotificationCampaign` record
   - Store job details, event ID, and metadata
   - Set initial status to approved (no admin approval needed)

2. **Business Discovery**

   - Use `BusinessDiscoveryService` to find businesses
   - Search within 25-mile radius of job zip code
   - Filter by category if provided
   - Return businesses with complete data (email required)

### 4. Business Discovery Logic

```
Zip Code → Radius Search → Business Filtering → Distance Calculation → Recipient Creation
```

**Steps:**

1. **Geographic Search**

   - Use `ZipCodeService` to find zip codes within 25-mile radius
   - Fallback to exact zip code match if no results
   - Cap radius at maximum 75 miles

2. **Business Filtering**

   - Filter businesses by zip codes in radius
   - Ensure businesses have email addresses
   - Filter by category if specified
   - Calculate distances from job location

3. **Recipient Creation**

   - Create `JobNotificationRecipient` records
   - Store business details and distance
   - Set status to pending

### 5. Notification Processing

```
Recipients → Email Notifications → Status Updates → Campaign Completion
```

**Steps:**

1. **Business Account Verification**

   - Check if business email exists in User table
   - Determine if business has verified certificates and account
   - Route to appropriate notification flow

2. **Email Content Preparation**

   - **Job Information Masking**: Only show state and cleaning service type
   - **Hidden Details**: Customer address, specific budget, personal details
   - **Generic Content**: Focus on service type and urgency

3. **Unified Link Generation**

   - **Single URL**: `/instant-provider-dashboard?token={token}&job_id={jobId}`
   - **Frontend Integration**: Frontend handles routing based on user status and token
   - **Existing Business**: Frontend redirects to job detail page for immediate claiming
   - **New Business**: Frontend handles account creation, certificate upload, and verification flow

4. **Direct Notification**

   - Send `JobNotificationMail` to each business
   - Include job details, customer information, and contact methods
   - Update recipient status to sent

5. **Campaign Completion**

   - Update campaign with final business count
   - Set status to completed
   - Log success metrics

### 6. Frontend Integration Flow

```
Business Click Email Link → Frontend Parse Token → API Calls → Account Creation/Job Claiming
```

**Steps:**

1. **Link Parsing**

   - Frontend extracts `token` and `job_id` from URL
   - Call `GET /api/business-info?token={token}&job_id={jobId}`
   - Determine business account status and certificate verification

2. **Existing Business Flow**

   - If `has_existing_account: true` and `certificates_verified: true`
   - Redirect to job detail page
   - Allow immediate job claiming

3. **New Business Flow**

   - If `has_existing_account: false`
   - Show account creation form with pre-filled business info
   - Guide through certificate upload process
   - Call `PATCH /api/user/certificates` to upload certificates
   - Call `POST /api/user/certificates/request-review` to request verification
   - Wait for admin approval before job claiming

4. **Certificate Verification (Admin)**

   - Admin receives notification of certificate review request
   - Admin reviews certificates via admin panel
   - Call `POST /api/admin/certificates/reviews/{userId}/approve` or `/reject`
   - Business notified of verification result

### 7. Customer Job Tracking Flow

```
Customer Login → Job History API → View Job Status → Track Claims
```

**Steps:**

1. **Customer Authentication**

   - Customer logs into system with email
   - System identifies customer by email address

2. **Job History Retrieval**

   - Call `GET /api/customer/job-history` to get all jobs
   - Filter by customer email from authenticated user
   - Return paginated list with claim status

3. **Job Details View**

   - Call `GET /api/customer/job-history/{campaignId}` for specific job
   - Show detailed claim information
   - Display provider details, bids, and booking status

### 8. Retry Mechanism (If No Businesses Found)

```
No Businesses → RetryJobNotificationJob → Delayed Retry → Expanded Search
```

**Steps:**

1. **Retry Setup**

   - Set campaign status to pending retry
   - Dispatch `RetryJobNotificationJob` with delay
   - Use exponential backoff (120s, 300s, 600s)

2. **Expanded Search**

   - Increase search radius on retries
   - Relax data completeness requirements
   - Continue until businesses found or max retries reached

## Response Specifications

### Success Response (200)

```json
{
  "status": "success",
  "message": "JobPostedEvent received and queued for processing",
  "event_id": "string"
}
```

### Validation Error Response (422)

```json
{
  "status": "error",
  "message": "Invalid webhook payload",
  "errors": {
    "field_name": ["validation message"]
  }
}
```

### Server Error Response (500)

```json
{
  "status": "error",
  "message": "Webhook processing failed"
}
```

## Configuration Requirements

### Environment Variables

```env
# Webhook authentication
WEBHOOK_TOKEN=your_secure_token_here

# Job notification settings
JOB_NOTIFICATION_DEFAULT_RADIUS=25
JOB_NOTIFICATION_MAX_RADIUS=75
JOB_NOTIFICATION_RETRY_BASE_DELAY=120
```

### Queue Configuration

- **Queue Name**: `default` (configurable)
- **Retry Attempts**: 3
- **Backoff Strategy**: 60 seconds between retries
- **Job Timeout**: Configurable via queue settings

## Error Handling & Monitoring

### Error Scenarios

1. **Authentication Failure**: Invalid or missing webhook token
2. **Validation Errors**: Missing required fields or invalid data types
3. **Business Discovery Failure**: No businesses found in radius
4. **Email Delivery Failure**: SMTP errors or invalid email addresses
5. **Queue Processing Errors**: Job failures or timeout issues

### Logging Strategy

- **Info Level**: Successful webhook reception, business discovery, notifications sent
- **Warning Level**: Validation failures, retry attempts
- **Error Level**: Authentication failures, processing errors, queue failures

### Monitoring Metrics

- Webhook reception rate
- Business discovery success rate
- Notification delivery success rate
- Average processing time
- Retry frequency and success rate

## Security Considerations

### Authentication

- Secure token-based authentication
- Timing-attack safe comparison
- Token rotation capability

### Data Validation

- Strict input validation
- SQL injection prevention via Eloquent ORM
- XSS prevention through proper encoding

### Rate Limiting

- Consider implementing rate limiting for webhook endpoints
- Monitor for abuse patterns

## Performance Considerations

### Queue Processing

- Asynchronous job processing for scalability
- Configurable retry mechanisms
- Job timeout handling

### Database Optimization

- Indexed queries on zip codes and business data
- Efficient geographic distance calculations
- Batch processing for large recipient lists

### Caching Strategy

- Cache zip code radius calculations
- Cache business data for repeated lookups
- Implement cache invalidation strategies

## Testing Requirements

### Unit Tests

- Webhook authentication validation
- Payload validation rules
- Data transformation logic
- Business discovery algorithms

### Integration Tests

- End-to-end webhook processing
- Queue job execution
- Email notification delivery
- Retry mechanism functionality

### Load Tests

- Concurrent webhook processing
- Queue performance under load
- Database query optimization

## Deployment Considerations

### Environment Setup

- Configure webhook tokens per environment
- Set up queue workers for job processing
- Configure email service for notifications

### Monitoring Setup

- Set up logging aggregation
- Configure alerting for webhook failures
- Monitor queue performance metrics

### Backup & Recovery

- Database backup strategies
- Queue job recovery mechanisms
- Webhook event replay capability
