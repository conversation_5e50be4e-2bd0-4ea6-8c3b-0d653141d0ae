# Testing Guide: Job Posted Webhook Flow

## Overview

This document provides comprehensive testing instructions for the complete job posted webhook flow, including webhook reception, business discovery, email notifications, frontend integration, and customer job tracking.

## Prerequisites

### Environment Setup

1. **Database**: Ensure all migrations are run

   ```bash
   php artisan migrate
   ```

2. **Queue Workers**: Start queue workers for background job processing

   ```bash
   php artisan queue:work
   ```

3. **Environment Variables**: Configure required environment variables

   ```env
   WEBHOOK_TOKEN=your_secure_webhook_token_here
   JOB_NOTIFICATION_DEFAULT_RADIUS=25
   JOB_NOTIFICATION_MAX_RADIUS=75
   JOB_NOTIFICATION_ADMIN_EMAIL=<EMAIL>
   JOB_NOTIFICATION_ADMIN_CONTACT_EMAIL=<EMAIL>
   JOB_NOTIFICATION_ADMIN_CONTACT_PHONE=******-123-4567
   JOBON_WEBAPP_URL=https://jobon.app
   ```

4. **Email Configuration**: Configure SMTP settings for email testing
   ```env
   MAIL_MAILER=smtp
   MAIL_HOST=your_smtp_host
   MAIL_PORT=587
   MAIL_USERNAME=your_email
   MAIL_PASSWORD=your_password
   MAIL_ENCRYPTION=tls
   MAIL_FROM_ADDRESS=<EMAIL>
   MAIL_FROM_NAME="JobON"
   ```

### Test Data Setup

1. **Businesses**: Create test businesses with different scenarios

   ```sql
   -- Existing business with verified certificates
   INSERT INTO businesses (name, email, phone, address, zip_code, category)
   VALUES ('Test Business 1', '<EMAIL>', '+**********', '123 Main St', '12345', 'cleaning');

   INSERT INTO users (name, email, password, certificates_status)
   VALUES ('Test Business 1', '<EMAIL>', 'password', 'approved');

   -- New business without account
   INSERT INTO businesses (name, email, phone, address, zip_code, category)
   VALUES ('Test Business 2', '<EMAIL>', '+**********', '456 Oak St', '12345', 'cleaning');
   ```

2. **Customer**: Create test customer account
   ```sql
   INSERT INTO users (name, email, password)
   VALUES ('Test Customer', '<EMAIL>', 'password');
   ```

## Test Scenarios

### 1. Webhook Reception & Validation

#### Test Case 1.1: Valid Webhook Request

**Objective**: Verify webhook accepts valid job posted event

**Steps**:

1. Send POST request to `/api/webhooks/job-posted`
2. Include valid authentication token
3. Send valid payload

**Request**:

```bash
curl -X POST http://localhost:8000/api/webhooks/job-posted \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Token: your_secure_webhook_token_here" \
  -d '{
    "event_type": "job_posted",
    "event_id": "test-event-123",
    "timestamp": "2024-01-15T10:00:00Z",
    "job_data": {
      "job_id": "job-test-123",
      "title": "House Cleaning Service",
      "description": "Need house cleaning for 3-bedroom apartment",
      "category_id": 1,
      "zip_code": "12345",
      "address": "789 Pine St, City, State 12345",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "budget_min": 100,
      "budget_max": 200,
      "currency": "USD",
      "customer_email": "<EMAIL>",
      "customer_name": "Test Customer",
      "phone": "+**********",
      "service_date": "2024-01-20",
      "location": "Living room, kitchen, bathrooms",
      "service": "Deep cleaning",
      "duration": "3 hours",
      "frequency": "one-time",
      "created_at": "2024-01-15T10:00:00Z"
    }
  }'
```

**Expected Response**:

```json
{
  "status": "success",
  "message": "JobPostedEvent received and queued for processing",
  "event_id": "test-event-123"
}
```

**Verification**:

- Check database for `JobNotificationCampaign` record
- Verify campaign status is "approved"
- Check queue for `ProcessJobNotificationJob`

#### Test Case 1.2: Invalid Authentication

**Objective**: Verify webhook rejects invalid authentication

**Steps**:

1. Send POST request with invalid token
2. Verify 401/403 response

**Request**:

```bash
curl -X POST http://localhost:8000/api/webhooks/job-posted \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Token: invalid_token" \
  -d '{"event_type": "job_posted", ...}'
```

**Expected Response**:

```json
{
  "status": "error",
  "message": "Webhook processing failed"
}
```

#### Test Case 1.3: Invalid Payload

**Objective**: Verify webhook validates required fields

**Steps**:

1. Send POST request with missing required fields
2. Verify 422 response with validation errors

**Request**:

```bash
curl -X POST http://localhost:8000/api/webhooks/job-posted \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Token: your_secure_webhook_token_here" \
  -d '{
    "event_type": "job_posted",
    "event_id": "test-event-123"
  }'
```

**Expected Response**:

```json
{
  "status": "error",
  "message": "Invalid webhook payload",
  "errors": {
    "timestamp": ["The timestamp field is required."],
    "job_data": ["The job data field is required."]
  }
}
```

### 2. Business Discovery & Email Notification

#### Test Case 2.1: Business Discovery (25-mile radius)

**Objective**: Verify businesses are found within 25-mile radius

**Steps**:

1. Send valid webhook request
2. Monitor queue processing
3. Check business discovery results

**Verification**:

- Check `JobNotificationCampaign.business_count` field
- Verify businesses found within 25-mile radius
- Check `JobNotificationRecipient` records created

**Database Check**:

```sql
SELECT
  jnc.id as campaign_id,
  jnc.job_id,
  jnc.business_count,
  jnc.status,
  COUNT(jnr.id) as recipient_count
FROM job_notification_campaigns jnc
LEFT JOIN job_notification_recipients jnr ON jnc.id = jnr.job_notification_campaign_id
WHERE jnc.job_id = 'job-test-123'
GROUP BY jnc.id;
```

#### Test Case 2.2: Email Notification Sent

**Objective**: Verify email notifications are sent to businesses

**Steps**:

1. Monitor email sending process
2. Check recipient status updates
3. Verify email content

**Verification**:

- Check `JobNotificationRecipient.status` = "sent"
- Verify email sent to business email addresses
- Check email content includes unified URL

**Database Check**:

```sql
SELECT
  jnr.business_email,
  jnr.status,
  jnr.sent_at,
  jnr.business_name
FROM job_notification_recipients jnr
JOIN job_notification_campaigns jnc ON jnr.job_notification_campaign_id = jnc.id
WHERE jnc.job_id = 'job-test-123';
```

### 3. Frontend Integration Testing

#### Test Case 3.1: Business Info API (Existing Business)

**Objective**: Verify API returns correct info for existing business

**Steps**:

1. Extract token and job_id from email URL
2. Call business info API
3. Verify response for existing business

**Request**:

```bash
curl -X GET "http://localhost:8000/api/business-info?token=YOUR_TOKEN&job_id=job-test-123"
```

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "business": {
      "id": 1,
      "name": "Test Business 1",
      "email": "<EMAIL>",
      "phone": "+**********",
      "address": "123 Main St",
      "distance": 2.5
    },
    "job": {
      "id": "job-test-123",
      "title": "House Cleaning Service",
      "description": "Need house cleaning for 3-bedroom apartment",
      "zip_code": "12345",
      "category": 1,
      "budget": 150.0,
      "service_date": "2024-01-20"
    },
    "has_existing_account": true,
    "user_id": 1,
    "certificates_verified": true,
    "token_expires_at": "2024-01-16T10:00:00.000000Z"
  }
}
```

#### Test Case 3.2: Business Info API (New Business)

**Objective**: Verify API returns correct info for new business

**Steps**:

1. Use token for business without account
2. Call business info API
3. Verify response for new business

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "business": {
      "id": 2,
      "name": "Test Business 2",
      "email": "<EMAIL>",
      "phone": "+**********",
      "address": "456 Oak St",
      "distance": 1.8
    },
    "job": {
      "id": "job-test-123",
      "title": "House Cleaning Service",
      "description": "Need house cleaning for 3-bedroom apartment",
      "zip_code": "12345",
      "category": 1,
      "budget": 150.0,
      "service_date": "2024-01-20"
    },
    "has_existing_account": false,
    "user_id": null,
    "certificates_verified": false,
    "token_expires_at": "2024-01-16T10:00:00.000000Z"
  }
}
```

#### Test Case 3.3: Job Details API

**Objective**: Verify job details API returns complete information

**Request**:

```bash
curl -X GET "http://localhost:8000/api/business-info/job-details?token=YOUR_TOKEN&job_id=job-test-123"
```

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "job": {
      "id": "job-test-123",
      "title": "House Cleaning Service",
      "description": "Need house cleaning for 3-bedroom apartment",
      "zip_code": "12345",
      "address": "789 Pine St, City, State 12345",
      "latitude": 40.7128,
      "longitude": -74.006,
      "category": 1,
      "budget": 150.0,
      "budget_min": 100,
      "budget_max": 200,
      "currency": "USD",
      "service_date": "2024-01-20",
      "location": "Living room, kitchen, bathrooms",
      "service": "Deep cleaning",
      "duration": "3 hours",
      "frequency": "one-time",
      "created_at": "2024-01-15T10:00:00.000000Z"
    },
    "customer": {
      "name": "Test Customer",
      "email": "<EMAIL>",
      "phone": "+**********"
    },
    "token_expires_at": "2024-01-16T10:00:00.000000Z"
  }
}
```

### 4. Certificate Upload & Verification Testing

#### Test Case 4.1: Certificate Upload

**Objective**: Verify certificate upload for new business

**Steps**:

1. Create new business account
2. Upload certificate files
3. Request certificate review

**Request**:

```bash
# First, upload certificate files
curl -X POST http://localhost:8000/api/assets/upload \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -F "files[]=@certificate1.pdf" \
  -F "files[]=@certificate2.jpg" \
  -F "collection=certificates"

# Then, update user certificates
curl -X PATCH http://localhost:8000/api/user/certificates \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "certificates": ["uuid1", "uuid2"]
  }'

# Finally, request certificate review
curl -X POST http://localhost:8000/api/user/certificates/request-review \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"
```

#### Test Case 4.2: Admin Certificate Verification

**Objective**: Verify admin can approve/reject certificates

**Steps**:

1. Login as admin
2. List certificate reviews
3. Approve/reject certificates

**Request**:

```bash
# List certificate reviews
curl -X GET "http://localhost:8000/api/admin/certificates/reviews" \
  -H "Authorization: Bearer ADMIN_AUTH_TOKEN"

# Approve certificates
curl -X POST "http://localhost:8000/api/admin/certificates/reviews/1/approve" \
  -H "Authorization: Bearer ADMIN_AUTH_TOKEN"

# Reject certificates
curl -X POST "http://localhost:8000/api/admin/certificates/reviews/1/reject" \
  -H "Authorization: Bearer ADMIN_AUTH_TOKEN"
```

### 5. Customer Job History Testing

#### Test Case 5.1: Customer Job History List

**Objective**: Verify customer can view all their jobs

**Steps**:

1. Login as customer
2. Call job history API
3. Verify pagination and filtering

**Request**:

```bash
curl -X GET "http://localhost:8000/api/customer/job-history?page=1&per_page=10" \
  -H "Authorization: Bearer CUSTOMER_AUTH_TOKEN"
```

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "campaigns": [
      {
        "id": 1,
        "job_id": "job-test-123",
        "job_title": "House Cleaning Service",
        "job_budget": 150.0,
        "job_zip_code": "12345",
        "customer_email": "<EMAIL>",
        "business_count": 5,
        "sent_count": 5,
        "claim_status": {
          "claimed": false,
          "status": "no_bids",
          "message": "No bids received yet"
        },
        "recipients_summary": {
          "total": 5,
          "pending": 0,
          "sent": 5,
          "failed": 0
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 1,
      "last_page": 1
    }
  }
}
```

#### Test Case 5.2: Customer Job Campaign Details

**Objective**: Verify customer can view detailed job information

**Request**:

```bash
curl -X GET "http://localhost:8000/api/customer/job-history/1" \
  -H "Authorization: Bearer CUSTOMER_AUTH_TOKEN"
```

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "campaign": {
      "id": 1,
      "job_id": "job-test-123",
      "job_title": "House Cleaning Service",
      "claim_status": {
        "claimed": false,
        "status": "no_bids",
        "message": "No bids received yet"
      }
    },
    "claim_details": {
      "job_booking_exists": false,
      "message": "Job booking not created yet"
    },
    "recipients": [
      {
        "id": 1,
        "business_name": "Test Business 1",
        "business_email": "<EMAIL>",
        "business_phone": "+**********",
        "business_address": "123 Main St",
        "distance": 2.5,
        "status": "sent",
        "sent_at": "2024-01-15T10:05:00.000000Z",
        "business": {
          "id": 1,
          "name": "Test Business 1",
          "email": "<EMAIL>",
          "phone": "+**********",
          "address": "123 Main St",
          "category": "cleaning"
        }
      }
    ]
  }
}
```

### 6. End-to-End Flow Testing

#### Test Case 6.1: Complete Flow - New Business

**Objective**: Test complete flow from webhook to job claiming for new business

**Steps**:

1. Send webhook request
2. Verify email sent to new business
3. Business clicks email link
4. Frontend creates account
5. Upload certificates
6. Admin approves certificates
7. Business claims job
8. Customer views job status

**Verification Points**:

- Email sent with correct unified URL
- Business info API returns `has_existing_account: false`
- Account creation successful
- Certificate upload and approval successful
- Job claiming successful
- Customer can see job status in history

#### Test Case 6.2: Complete Flow - Existing Business

**Objective**: Test complete flow from webhook to job claiming for existing business

**Steps**:

1. Send webhook request
2. Verify email sent to existing business
3. Business clicks email link
4. Frontend redirects to job detail
5. Business claims job immediately
6. Customer views job status

**Verification Points**:

- Email sent with correct unified URL
- Business info API returns `has_existing_account: true` and `certificates_verified: true`
- Direct job claiming successful
- Customer can see job status in history

## Performance Testing

### Load Testing

1. **Concurrent Webhook Requests**: Send 100+ concurrent webhook requests
2. **Queue Processing**: Monitor queue performance under load
3. **Database Performance**: Check query performance with large datasets

### Stress Testing

1. **Large Business Discovery**: Test with 1000+ businesses in radius
2. **Email Sending**: Monitor email delivery performance
3. **API Response Times**: Ensure all APIs respond within acceptable time limits

## Security Testing

### Authentication Testing

1. **Webhook Token Security**: Test token validation and timing attacks
2. **API Authentication**: Verify all protected endpoints require authentication
3. **Authorization**: Ensure customers can only access their own jobs

### Data Validation Testing

1. **Input Sanitization**: Test with malicious input data
2. **SQL Injection**: Attempt SQL injection attacks
3. **XSS Prevention**: Test cross-site scripting prevention

## Monitoring & Logging

### Log Verification

1. **Webhook Logs**: Check webhook reception logs
2. **Queue Logs**: Monitor job processing logs
3. **Email Logs**: Verify email sending logs
4. **Error Logs**: Check error handling and logging

### Metrics Monitoring

1. **Webhook Success Rate**: Monitor webhook processing success rate
2. **Email Delivery Rate**: Track email delivery success rate
3. **API Response Times**: Monitor API performance
4. **Queue Processing Time**: Track background job processing time

## Troubleshooting

### Common Issues

1. **Queue Jobs Not Processing**: Check queue worker status
2. **Email Not Sending**: Verify SMTP configuration
3. **Business Discovery Failing**: Check zip code data and radius configuration
4. **Token Expiration**: Monitor token expiry times

### Debug Commands

```bash
# Check queue status
php artisan queue:work --verbose

# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear cache
php artisan cache:clear

# Check logs
tail -f storage/logs/laravel.log
```

## Test Data Cleanup

After testing, clean up test data:

```sql
-- Clean up test campaigns
DELETE FROM job_notification_recipients WHERE job_notification_campaign_id IN (
  SELECT id FROM job_notification_campaigns WHERE job_id LIKE 'job-test-%'
);
DELETE FROM job_notification_campaigns WHERE job_id LIKE 'job-test-%';

-- Clean up test businesses
DELETE FROM businesses WHERE email LIKE '%@test.com';
DELETE FROM users WHERE email LIKE '%@test.com';

-- Clean up test assets
DELETE FROM assets WHERE uploaded_by IN (
  SELECT id FROM users WHERE email LIKE '%@test.com'
);
```

This testing guide covers all aspects of the webhook flow and should be used by AI agents to verify the complete implementation.
